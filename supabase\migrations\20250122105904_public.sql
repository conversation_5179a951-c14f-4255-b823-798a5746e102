create type "public"."nylas_status" as enum ('STARTED', 'AUTHENTICATED', 'READY');

create table "public"."nylas_customers" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "code" text,
    "grant" text,
    "status" nylas_status not null default 'STARTED'::nylas_status,
    "customer_id" uuid not null default gen_random_uuid()
);


alter table "public"."nylas_customers" enable row level security;

CREATE UNIQUE INDEX nylas_customers_customer_id_key ON public.nylas_customers USING btree (customer_id);

CREATE UNIQUE INDEX nylas_customers_pkey ON public.nylas_customers USING btree (id);

alter table "public"."nylas_customers" add constraint "nylas_customers_pkey" PRIMARY KEY using index "nylas_customers_pkey";

alter table "public"."nylas_customers" add constraint "nylas_customers_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."nylas_customers" validate constraint "nylas_customers_customer_id_fkey";

alter table "public"."nylas_customers" add constraint "nylas_customers_customer_id_key" UNIQUE using index "nylas_customers_customer_id_key";

grant delete on table "public"."nylas_customers" to "authenticated";

grant insert on table "public"."nylas_customers" to "authenticated";

grant references on table "public"."nylas_customers" to "authenticated";

grant select on table "public"."nylas_customers" to "authenticated";

grant trigger on table "public"."nylas_customers" to "authenticated";

grant truncate on table "public"."nylas_customers" to "authenticated";

grant update on table "public"."nylas_customers" to "authenticated";

grant delete on table "public"."nylas_customers" to "service_role";

grant insert on table "public"."nylas_customers" to "service_role";

grant references on table "public"."nylas_customers" to "service_role";

grant select on table "public"."nylas_customers" to "service_role";

grant trigger on table "public"."nylas_customers" to "service_role";

grant truncate on table "public"."nylas_customers" to "service_role";

grant update on table "public"."nylas_customers" to "service_role";

create policy "Enable insert for authenticated users"
on "public"."nylas_customers"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable update for users based on email"
on "public"."nylas_customers"
as permissive
for update
to authenticated
using (true)
with check (true);



