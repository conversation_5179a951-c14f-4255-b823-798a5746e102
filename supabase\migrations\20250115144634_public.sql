create type "public"."workplace_type" as enum ('ONSITE', 'REMOTE', 'HYBRID');

alter table "public"."customer_onboarding" drop column "languages";

alter table "public"."customer_onboarding" drop column "locations";

alter table "public"."customer_onboarding" add column "company_excludes" text;

alter table "public"."customer_onboarding" add column "countries" text;

alter table "public"."customer_onboarding" add column "functional_preferences" text;

alter table "public"."customer_onboarding" add column "gender" text;

alter table "public"."customer_onboarding" add column "industry_excludes" text;

alter table "public"."customer_onboarding" add column "job_titles" text;

alter table "public"."customer_onboarding" add column "keywords" text;

alter table "public"."customer_onboarding" add column "languages_fluent" text[];

alter table "public"."customer_onboarding" add column "languages_learning" text;

alter table "public"."customer_onboarding" add column "other_info" text;

alter table "public"."customer_onboarding" add column "regions" text;

alter table "public"."customer_onboarding" add column "role_excludes" text;

alter table "public"."customer_onboarding" add column "seniority" text;

alter table "public"."customer_onboarding" add column "visa_sponsorship" boolean;

alter table "public"."customer_onboarding" add column "workpalce_type" workplace_type;


