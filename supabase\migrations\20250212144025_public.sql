drop policy "Enable insert for service role" on "public"."orders";

drop policy "Enable read access for authanticated users" on "public"."orders";

revoke delete on table "public"."orders" from "anon";

revoke insert on table "public"."orders" from "anon";

revoke references on table "public"."orders" from "anon";

revoke select on table "public"."orders" from "anon";

revoke trigger on table "public"."orders" from "anon";

revoke truncate on table "public"."orders" from "anon";

revoke update on table "public"."orders" from "anon";

revoke delete on table "public"."orders" from "authenticated";

revoke insert on table "public"."orders" from "authenticated";

revoke references on table "public"."orders" from "authenticated";

revoke select on table "public"."orders" from "authenticated";

revoke trigger on table "public"."orders" from "authenticated";

revoke truncate on table "public"."orders" from "authenticated";

revoke update on table "public"."orders" from "authenticated";

revoke delete on table "public"."orders" from "service_role";

revoke insert on table "public"."orders" from "service_role";

revoke references on table "public"."orders" from "service_role";

revoke select on table "public"."orders" from "service_role";

revoke trigger on table "public"."orders" from "service_role";

revoke truncate on table "public"."orders" from "service_role";

revoke update on table "public"."orders" from "service_role";

alter table "public"."orders" drop constraint "orders_customer_id_fkey";

alter table "public"."orders" drop constraint "orders_payment_id_fkey";

alter table "public"."orders" drop constraint "orders_payment_id_key";

alter table "public"."orders" drop constraint "orders_product_id_fkey";

alter table "public"."orders" drop constraint "Orders_pkey";

drop index if exists "public"."Orders_pkey";

drop index if exists "public"."orders_payment_id_key";

drop table "public"."orders";

create table "public"."plans" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "customer_id" uuid not null default gen_random_uuid(),
    "product_id" bigint not null,
    "status" order_status not null default 'OPEN'::order_status,
    "payment_id" bigint not null
);


alter table "public"."plans" enable row level security;

alter table "public"."products" add column "work_promised" integer;

CREATE UNIQUE INDEX "Orders_pkey" ON public.plans USING btree (id);

CREATE UNIQUE INDEX orders_payment_id_key ON public.plans USING btree (payment_id);

alter table "public"."plans" add constraint "Orders_pkey" PRIMARY KEY using index "Orders_pkey";

alter table "public"."plans" add constraint "orders_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."plans" validate constraint "orders_customer_id_fkey";

alter table "public"."plans" add constraint "orders_payment_id_fkey" FOREIGN KEY (payment_id) REFERENCES payments(id) not valid;

alter table "public"."plans" validate constraint "orders_payment_id_fkey";

alter table "public"."plans" add constraint "orders_payment_id_key" UNIQUE using index "orders_payment_id_key";

alter table "public"."plans" add constraint "orders_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) not valid;

alter table "public"."plans" validate constraint "orders_product_id_fkey";

grant delete on table "public"."plans" to "anon";

grant insert on table "public"."plans" to "anon";

grant references on table "public"."plans" to "anon";

grant select on table "public"."plans" to "anon";

grant trigger on table "public"."plans" to "anon";

grant truncate on table "public"."plans" to "anon";

grant update on table "public"."plans" to "anon";

grant delete on table "public"."plans" to "authenticated";

grant insert on table "public"."plans" to "authenticated";

grant references on table "public"."plans" to "authenticated";

grant select on table "public"."plans" to "authenticated";

grant trigger on table "public"."plans" to "authenticated";

grant truncate on table "public"."plans" to "authenticated";

grant update on table "public"."plans" to "authenticated";

grant delete on table "public"."plans" to "service_role";

grant insert on table "public"."plans" to "service_role";

grant references on table "public"."plans" to "service_role";

grant select on table "public"."plans" to "service_role";

grant trigger on table "public"."plans" to "service_role";

grant truncate on table "public"."plans" to "service_role";

grant update on table "public"."plans" to "service_role";

create policy "Enable insert for service role"
on "public"."plans"
as permissive
for insert
to service_role
with check (true);


create policy "Enable read access for authanticated users"
on "public"."plans"
as permissive
for select
to authenticated
using (true);



