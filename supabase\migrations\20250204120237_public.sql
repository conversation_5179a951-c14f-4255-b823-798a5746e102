alter table "public"."nylas_customers" alter column "status" drop default;

alter type "public"."nylas_status" rename to "nylas_status__old_version_to_be_dropped";

create type "public"."nylas_status" as enum ('STARTED', 'AUTHENTICATED', 'READY', 'REVOKED', 'EXPIRED');

alter table "public"."nylas_customers" alter column status type "public"."nylas_status" using status::text::"public"."nylas_status";

alter table "public"."nylas_customers" alter column "status" set default 'STARTED'::nylas_status;

drop type "public"."nylas_status__old_version_to_be_dropped";


