create policy "Enable insert for authenticated users only"
on "public"."onboarding_locations"
as permissive
for insert
to authenticated
with check (true);


create policy "Enable read access for all users"
on "public"."onboarding_locations"
as permissive
for select
to authenticated
using (true);


create policy "Enable Delete for authenticated users only"
on "public"."onboarding_locations"
as permissive
for delete
to authenticated
using (true);

create policy "Enable update for authenticated users only"
on "public"."onboarding_locations"
as permissive
for update
to public
using (true)
with check (true);
