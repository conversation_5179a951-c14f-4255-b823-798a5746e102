create type "public"."order_status" as enum ('OPEN', 'COMPLETE');

create type "public"."product_status" as enum ('ACTIVE', 'INACTIVE');

drop policy "Enable insert for authenticated users only" on "public"."payments";

create table "public"."orders" (
    "id" bigint generated by default as identity not null,
    "created_at" timestamp with time zone not null default now(),
    "customer_id" uuid not null default gen_random_uuid(),
    "product_id" bigint not null,
    "status" order_status not null default 'OPEN'::order_status,
    "payment_id" bigint not null
);


alter table "public"."orders" enable row level security;

alter table "public"."products" add column "status" product_status not null default 'ACTIVE'::product_status;

CREATE UNIQUE INDEX "Orders_pkey" ON public.orders USING btree (id);

CREATE UNIQUE INDEX customer_onboarding_customer_id_key ON public.customer_onboarding USING btree (customer_id);

CREATE UNIQUE INDEX orders_payment_id_key ON public.orders USING btree (payment_id);

alter table "public"."orders" add constraint "Orders_pkey" PRIMARY KEY using index "Orders_pkey";

alter table "public"."customer_onboarding" add constraint "customer_onboarding_customer_id_key" UNIQUE using index "customer_onboarding_customer_id_key";

alter table "public"."orders" add constraint "orders_customer_id_fkey" FOREIGN KEY (customer_id) REFERENCES customers(id) not valid;

alter table "public"."orders" validate constraint "orders_customer_id_fkey";

alter table "public"."orders" add constraint "orders_payment_id_fkey" FOREIGN KEY (payment_id) REFERENCES payments(id) not valid;

alter table "public"."orders" validate constraint "orders_payment_id_fkey";

alter table "public"."orders" add constraint "orders_payment_id_key" UNIQUE using index "orders_payment_id_key";

alter table "public"."orders" add constraint "orders_product_id_fkey" FOREIGN KEY (product_id) REFERENCES products(id) not valid;

alter table "public"."orders" validate constraint "orders_product_id_fkey";

grant delete on table "public"."orders" to "anon";

grant insert on table "public"."orders" to "anon";

grant references on table "public"."orders" to "anon";

grant select on table "public"."orders" to "anon";

grant trigger on table "public"."orders" to "anon";

grant truncate on table "public"."orders" to "anon";

grant update on table "public"."orders" to "anon";

grant delete on table "public"."orders" to "authenticated";

grant insert on table "public"."orders" to "authenticated";

grant references on table "public"."orders" to "authenticated";

grant select on table "public"."orders" to "authenticated";

grant trigger on table "public"."orders" to "authenticated";

grant truncate on table "public"."orders" to "authenticated";

grant update on table "public"."orders" to "authenticated";

grant delete on table "public"."orders" to "service_role";

grant insert on table "public"."orders" to "service_role";

grant references on table "public"."orders" to "service_role";

grant select on table "public"."orders" to "service_role";

grant trigger on table "public"."orders" to "service_role";

grant truncate on table "public"."orders" to "service_role";

grant update on table "public"."orders" to "service_role";

create policy "Enable insert for service role"
on "public"."orders"
as permissive
for insert
to service_role
with check (true);


create policy "Enable read access for authanticated users"
on "public"."orders"
as permissive
for select
to authenticated
using (true);


create policy "Enable insert for service role"
on "public"."payments"
as permissive
for insert
to service_role
with check (true);



