# ApplySquad Supabase Backend

A comprehensive job application management system built with Supabase, featuring multi-agent support, customer management, and automated job application processing.

## 🏗️ Project Overview

ApplySquad is a job application service platform that connects customers with agents who help them apply for jobs. The system manages:

- **Customer Management**: User profiles, onboarding, and preferences
- **Agent Management**: Service providers who handle job applications
- **Job Management**: Job listings, applications, and tracking
- **Payment Processing**: Stripe integration for service payments
- **Document Management**: CV, cover letters, and application screenshots
- **Multi-Agent Support**: Multiple agents can serve customers

## 📋 Prerequisites

Before setting up the project, ensure you have:

- [Node.js](https://nodejs.org/) (v16 or higher)
- [Supabase CLI](https://supabase.com/docs/guides/cli)
- [Docker](https://www.docker.com/) (for local development)
- [Git](https://git-scm.com/)

## 🚀 Quick Setup

### 1. Install Supabase CLI

**Windows (Recommended - Scoop):**
```bash
# Install Scoop if not already installed
Set-ExecutionPolicy RemoteSigned -Scope CurrentUser
irm get.scoop.sh | iex

# Install Supabase CLI
scoop bucket add supabase https://github.com/supabase/scoop-bucket.git
scoop install supabase
```

**Windows (Alternative - Direct Download):**
1. Download from [GitHub Releases](https://github.com/supabase/cli/releases)
2. Extract to a folder (e.g., `C:\supabase`)
3. Add to PATH environment variable

**macOS:**
```bash
brew install supabase/tap/supabase
```

**Linux:**
```bash
# Download and install
curl -fsSL https://github.com/supabase/cli/releases/download/v1.200.3/supabase_linux_amd64.tar.gz | tar -xz
sudo mv supabase /usr/local/bin/
```

### 2. Clone and Setup

```bash
# Navigate to the project directory
cd supabase

# Start Supabase local development
supabase start
```

### 3. Environment Variables

Create a `.env.local` file in your project root:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# OpenAI (for AI features)
OPENAI_API_KEY=your_openai_api_key_here

# Stripe (for payments)
STRIPE_SECRET_KEY=your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret_here

# Email Configuration (optional)
SENDGRID_API_KEY=your_sendgrid_api_key_here

# SMS Configuration (optional)
SUPABASE_AUTH_SMS_TWILIO_AUTH_TOKEN=your_twilio_token_here

# S3 Configuration (optional)
S3_HOST=your_s3_host_here
S3_REGION=your_s3_region_here
S3_ACCESS_KEY=your_s3_access_key_here
S3_SECRET_KEY=your_s3_secret_key_here
```

## 🗄️ Database Schema

### Core Tables

1. **customers** - Customer profiles and information
2. **agents** - Service provider profiles
3. **agent_customers** - Many-to-many relationship between agents and customers
4. **jobs** - Job listings and opportunities
5. **customer_jobs** - Job applications and their status
6. **documents** - File storage references (CVs, cover letters, screenshots)
7. **payments** - Payment records and transactions
8. **admins** - Administrative users
9. **products** - Service packages and pricing
10. **customer_onboarding** - Customer preferences and requirements
11. **cover_letters** - Generated cover letters for applications

### Key Features

- **Row Level Security (RLS)** enabled on all tables
- **Multi-agent support** with proper relationship management
- **Document management** with Supabase Storage integration
- **Payment tracking** with Stripe integration
- **Automated timestamps** with `moddatetime` extension

## 🔄 Migration Management

### Current Migration Status

The project has **40+ migrations** covering:
- Initial schema setup (January 2025)
- Multi-agent support (January 2025)
- Customer onboarding enhancements (February-March 2025)
- Cover letter generation (March 2025)
- Product code updates (May 2025)

### Running Migrations

```bash
# Apply all pending migrations
supabase db reset

# Create a new migration
supabase db diff -f migration_name

# Apply specific migration
supabase migration up
```

### Migration Workflow

1. **Make schema changes** in Supabase Studio or via SQL
2. **Generate migration** using `supabase db diff -f migration_name`
3. **Review migration file** in `migrations/` directory
4. **Test migration** in local environment
5. **Apply to production** using Supabase Dashboard or CLI

## 🛠️ Development Workflow

### Local Development

```bash
# Start all services
supabase start

# View service URLs
supabase status

# Access Supabase Studio
# Navigate to http://127.0.0.1:54323

# View logs
supabase logs

# Stop services
supabase stop
```

### Service Ports

- **API**: http://127.0.0.1:54321
- **Studio**: http://127.0.0.1:54323
- **Database**: postgresql://postgres:postgres@127.0.0.1:54322/postgres
- **Inbucket (Email)**: http://127.0.0.1:54324
- **Analytics**: http://127.0.0.1:54327

## 📁 Project Structure

```
supabase/
├── config.toml              # Supabase configuration
├── migrations/              # Database migrations (40+ files)
│   ├── 20250107140110_public.sql
│   ├── 20250129203600_multi_agent.sql
│   └── ...
├── data/                    # Seed data
│   └── products.sql         # Product catalog
├── migration.sh             # Migration helper script
└── README.md               # This file
```

## 🔧 Configuration Details

### Authentication
- **Email signup**: Enabled
- **Email confirmations**: Disabled (for development)
- **JWT expiry**: 1 hour
- **Password requirements**: Minimum 6 characters

### Storage
- **File size limit**: 50MiB
- **Image transformation**: Enabled
- **Buckets**: Configured for document storage

### Edge Functions
- **stripe-payment**: Payment processing
- **nylas-webhook**: Email integration
- **job-created**: Job notification handling

## 🚨 Troubleshooting

### Common Issues

1. **Supabase CLI not found**
   ```bash
   npm install -g @supabase/cli
   ```

2. **Docker not running**
   ```bash
   # Start Docker Desktop or Docker daemon
   docker --version
   ```

3. **Port conflicts**
   ```bash
   # Check if ports are in use
   supabase stop
   supabase start
   ```

4. **Migration errors**
   ```bash
   # Reset database and reapply migrations
   supabase db reset
   ```

### Getting Help

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Discord](https://discord.supabase.com)
- [GitHub Issues](https://github.com/supabase/supabase/issues)

## 📊 What's Already Done

✅ **Complete database schema** with 11+ tables
✅ **40+ migrations** covering all features
✅ **Row Level Security** policies
✅ **Multi-agent support** architecture
✅ **Payment integration** setup
✅ **Document management** system
✅ **Edge Functions** configuration
✅ **Authentication** setup
✅ **Storage** configuration
✅ **Seed data** for products

## 🎯 Next Steps

1. **Install Supabase CLI** if not already installed
2. **Run `supabase start`** to initialize local environment
3. **Configure environment variables** for external services
4. **Test the setup** by accessing Supabase Studio
5. **Deploy to production** when ready

---

**Note**: This is a production-ready Supabase backend with comprehensive features for job application management. All core functionality is implemented and ready for use.
