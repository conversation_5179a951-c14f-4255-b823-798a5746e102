alter table "public"."customer_jobs" alter column "status" drop default;

alter type "public"."customer_job_status" rename to "customer_job_status__old_version_to_be_dropped";

create type "public"."customer_job_status" as enum ('NEW', 'APPROVED', 'DECLINED', 'APPLIED', 'SATISFIED', 'DISSA<PERSON>SFIED', 'DELETED', 'CUSTOMER_APPLIED', 'EXPIRED');

alter table "public"."customer_jobs" alter column status type "public"."customer_job_status" using status::text::"public"."customer_job_status";

alter table "public"."customer_jobs" alter column "status" set default 'NEW'::customer_job_status;

drop type "public"."customer_job_status__old_version_to_be_dropped";

create policy "Update job for agents"
on "public"."jobs"
as permissive
for update
to authenticated
using ((( SELECT auth.uid() AS uid) IN ( SELECT agents.id
   FROM agents
  WHERE (auth.uid() = agents.id))));



